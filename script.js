// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });
    
    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Form Handling - Updated for multi-step form
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('booking-form');
    if (!form) return;
    
    const gateCodeRadios = document.querySelectorAll('input[name="hasGateCode"]');
    const gateCodeGroup = document.getElementById('gateCodeGroup');
    const gateCodeInput = document.getElementById('gateCode');
    
    // Show/hide gate code input based on radio selection
    gateCodeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'yes') {
                gateCodeGroup.style.display = 'block';
                gateCodeInput.required = true;
            } else {
                gateCodeGroup.style.display = 'none';
                gateCodeInput.required = false;
                gateCodeInput.value = '';
            }
        });
    });
    
    // Form validation and submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            submitForm();
        }
    });
    
    // Real-time validation
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('invalid')) {
                validateField(this);
            }
        });
    });
    
    // Initialize multi-step form
    showStep(1);
    
    // Initialize auto-save and load saved data
    loadSavedFormData();
    autoSaveForm();
});

// Form Validation Functions
function validateForm() {
    const form = document.getElementById('booking-form');
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    // Validate phone number format
    const phoneInput = document.getElementById('cellPhone');
    if (!validatePhoneNumber(phoneInput.value)) {
        showError(phoneInput, 'Please enter a valid phone number');
        isValid = false;
    }
    
    // Validate zip code format
    const zipInput = document.getElementById('zipCode');
    if (!validateZipCode(zipInput.value)) {
        showError(zipInput, 'Please enter a valid 5-digit zip code');
        isValid = false;
    }
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    
    // Clear previous error
    clearError(field);
    
    // Check if required field is empty
    if (field.hasAttribute('required') && !value) {
        showError(field, 'This field is required');
        return false;
    }
    
    // Specific validations
    switch (field.type) {
        case 'email':
            if (value && !validateEmail(value)) {
                showError(field, 'Please enter a valid email address');
                return false;
            }
            break;
        case 'tel':
            if (value && !validatePhoneNumber(value)) {
                showError(field, 'Please enter a valid phone number');
                return false;
            }
            break;
    }
    
    // Zip code validation
    if (field.id === 'zipCode' && value && !validateZipCode(value)) {
        showError(field, 'Please enter a valid 5-digit zip code');
        return false;
    }
    
    return true;
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePhoneNumber(phone) {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    // Check if it's 10 digits (US phone number)
    return cleanPhone.length === 10;
}

function validateZipCode(zip) {
    const zipRegex = /^\d{5}$/;
    return zipRegex.test(zip);
}

function showError(field, message) {
    field.classList.add('invalid');
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message show';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

function clearError(field) {
    field.classList.remove('invalid');
    const errorMessage = field.parentNode.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
}

// Form Submission - Updated for multi-step
function submitForm() {
    const form = document.getElementById('booking-form');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    // Show loading state
    submitBtn.classList.add('loading');
    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;
    
    // Collect form data
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // Add timestamp
    data.submittedAt = new Date().toISOString();
    
    // Simulate form submission
    setTimeout(() => {
        console.log('Form submitted with data:', data);
        
        // Show success message
        showSuccessMessage();
        
        // Reset form and go back to step 1
        form.reset();
        currentStep = 1;
        showStep(1);
        
        // Hide gate code group if it was shown
        const gateCodeGroup = document.getElementById('gateCodeGroup');
        if (gateCodeGroup) {
            gateCodeGroup.style.display = 'none';
        }
        
        // Reset button state
        submitBtn.classList.remove('loading');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
        // Clear saved data
        clearSavedFormData();
        
        // Scroll to top of form
        document.getElementById('booking').scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
        
    }, 2000);
}

function showSuccessMessage() {
    // Remove existing success message
    const existingSuccess = document.querySelector('.success-message');
    if (existingSuccess) {
        existingSuccess.remove();
    }
    
    // Create success message
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message show';
    successDiv.innerHTML = `
        <strong>Thank you!</strong> Your booking request has been submitted successfully. 
        We'll contact you within 24 hours to confirm your service and discuss payment options.
    `;
    
    // Insert at top of form
    const form = document.getElementById('booking-form');
    form.insertBefore(successDiv, form.firstChild);
    
    // Auto-hide after 10 seconds
    setTimeout(() => {
        successDiv.classList.remove('show');
        setTimeout(() => {
            successDiv.remove();
        }, 300);
    }, 10000);
}

// Phone number formatting
const cellPhoneInput = document.getElementById('cellPhone');
if (cellPhoneInput) {
    cellPhoneInput.addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    let formattedValue = '';
    
    if (value.length > 0) {
        if (value.length <= 3) {
            formattedValue = `(${value}`;
        } else if (value.length <= 6) {
            formattedValue = `(${value.slice(0, 3)}) ${value.slice(3)}`;
        } else {
            formattedValue = `(${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6, 10)}`;
        }
    }
    
        e.target.value = formattedValue;
    });
}

// Zip code formatting (numbers only)
const zipCodeInput = document.getElementById('zipCode');
if (zipCodeInput) {
    zipCodeInput.addEventListener('input', function(e) {
        e.target.value = e.target.value.replace(/\D/g, '').slice(0, 5);
    });
}

// Navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.15)';
    } else {
        navbar.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    }
});

// Animate service cards on scroll
function animateOnScroll() {
    const cards = document.querySelectorAll('.service-card, .contact-card');
    
    cards.forEach(card => {
        const cardTop = card.getBoundingClientRect().top;
        const cardVisible = 150;
        
        if (cardTop < window.innerHeight - cardVisible) {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }
    });
}

// Initialize card animation
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.service-card, .contact-card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    });
    
    animateOnScroll();
});

window.addEventListener('scroll', animateOnScroll);

// Form auto-save to localStorage (optional feature)
function autoSaveForm() {
    const form = document.getElementById('booking-form');
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            const formData = {};
            inputs.forEach(inp => {
                if (inp.type === 'radio') {
                    if (inp.checked) {
                        formData[inp.name] = inp.value;
                    }
                } else {
                    formData[inp.name] = inp.value;
                }
            });
            localStorage.setItem('trashServiceBooking', JSON.stringify(formData));
        });
    });
}

// Load saved form data
function loadSavedFormData() {
    const savedData = localStorage.getItem('trashServiceBooking');
    if (savedData) {
        const formData = JSON.parse(savedData);
        const form = document.getElementById('booking-form');
        
        Object.keys(formData).forEach(key => {
            const element = form.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'radio') {
                    const radioButton = form.querySelector(`[name="${key}"][value="${formData[key]}"]`);
                    if (radioButton) {
                        radioButton.checked = true;
                        // Trigger change event to show/hide gate code field
                        radioButton.dispatchEvent(new Event('change'));
                    }
                } else {
                    element.value = formData[key];
                }
            }
        });
    }
}

// Clear saved data on successful submission
function clearSavedFormData() {
    localStorage.removeItem('trashServiceBooking');
}

// Initialize auto-save functionality
document.addEventListener('DOMContentLoaded', function() {
    loadSavedFormData();
    autoSaveForm();
});

// Add to successful submission
const originalSubmitForm = submitForm;
submitForm = function() {
    originalSubmitForm();
    // Clear saved data after successful submission
    setTimeout(() => {
        clearSavedFormData();
    }, 2000);
};


// Observe service cards and FAQ items
        document.querySelectorAll('.service-card, .faq-item').forEach(card => {
            observer.observe(card);
        });

        // FAQ Toggle Function
        function toggleFAQ(element) {
            const faqItem = element.parentNode;
            const answer = faqItem.querySelector('.faq-answer');
            const icon = element.querySelector('.faq-icon');
            
            // Close all other FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                if (item !== faqItem) {
                    const otherQuestion = item.querySelector('.faq-question');
                    const otherAnswer = item.querySelector('.faq-answer');
                    const otherIcon = item.querySelector('.faq-icon');
                    
                    otherQuestion.classList.remove('active');
                    otherAnswer.classList.remove('active');
                    otherIcon.textContent = '+';
                }
            });
            
            // Toggle current FAQ item
            if (element.classList.contains('active')) {
                element.classList.remove('active');
                answer.classList.remove('active');
                icon.textContent = '+';
            } else {
                element.classList.add('active');
                answer.classList.add('active');
                icon.textContent = '×';
            }
        }

// Multi-step form functionality
let currentStep = 1;
const totalSteps = 3;

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.form-step').forEach(stepEl => {
        stepEl.classList.remove('active');
    });
    
    // Show current step
    const currentStepEl = document.querySelector(`.form-step[data-step="${step}"]`);
    if (currentStepEl) {
        currentStepEl.classList.add('active');
    }
    
    // Update progress indicator
    updateProgressIndicator(step);
    
    // Update progress bar
    const progressFill = document.getElementById('progressFill');
    if (progressFill) {
        const progressPercentage = (step / totalSteps) * 100;
        progressFill.style.width = progressPercentage + '%';
    }
}

function updateProgressIndicator(step) {
    document.querySelectorAll('.progress-step').forEach((stepEl, index) => {
        const stepNumber = index + 1;
        stepEl.classList.remove('active', 'completed');
        
        if (stepNumber === step) {
            stepEl.classList.add('active');
        } else if (stepNumber < step) {
            stepEl.classList.add('completed');
        }
    });
}

function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
            currentStep++;
            showStep(currentStep);
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
    }
}

function validateCurrentStep() {
    const currentStepEl = document.querySelector(`.form-step[data-step="${currentStep}"]`);
    if (!currentStepEl) return false;
    
    const requiredFields = currentStepEl.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Additional validation for specific steps
    if (currentStep === 1) {
        const phoneInput = document.getElementById('cellPhone');
        if (phoneInput && phoneInput.value && !validatePhoneNumber(phoneInput.value)) {
            showError(phoneInput, 'Please enter a valid phone number');
            isValid = false;
        }
    }
    
    if (currentStep === 2) {
        const zipInput = document.getElementById('zipCode');
        if (zipInput && zipInput.value && !validateZipCode(zipInput.value)) {
            showError(zipInput, 'Please enter a valid 5-digit zip code');
            isValid = false;
        }
    }
    
    return isValid;
}

// Initialize multi-step form
document.addEventListener('DOMContentLoaded', function() {
    showStep(1);
});




